#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules/rolldown-vite/bin/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules/rolldown-vite/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules/rolldown-vite/bin/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules/rolldown-vite/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules/rolldown-vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../../rolldown-vite@7.0.1_@types+_fa09b3401f3afb8d4f210b826f4f76e6/node_modules/rolldown-vite/bin/vite.js" "$@"
fi
