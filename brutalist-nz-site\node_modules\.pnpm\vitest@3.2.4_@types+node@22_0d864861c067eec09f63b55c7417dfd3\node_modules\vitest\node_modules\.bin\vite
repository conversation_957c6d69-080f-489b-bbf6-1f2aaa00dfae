#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules/vite/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules/vite/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules/vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../../../vite@7.0.0_@types+node@22.1_bb9deac2b8e94ba6468ab14ee1bb48b7/node_modules/vite/bin/vite.js" "$@"
fi
