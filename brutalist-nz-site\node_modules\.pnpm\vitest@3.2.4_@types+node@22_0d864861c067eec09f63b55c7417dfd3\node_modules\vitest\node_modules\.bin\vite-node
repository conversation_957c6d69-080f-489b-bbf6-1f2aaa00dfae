#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite-node@3.2.4_@types+node_77ec93dc1f1c75018c1116de0ff9a166/node_modules/vite-node/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite-node@3.2.4_@types+node_77ec93dc1f1c75018c1116de0ff9a166/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite-node@3.2.4_@types+node_77ec93dc1f1c75018c1116de0ff9a166/node_modules/vite-node/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vite-node@3.2.4_@types+node_77ec93dc1f1c75018c1116de0ff9a166/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../vite-node@3.2.4_@types+node_77ec93dc1f1c75018c1116de0ff9a166/node_modules/vite-node/vite-node.mjs" "$@"
else
  exec node  "$basedir/../../../../../vite-node@3.2.4_@types+node_77ec93dc1f1c75018c1116de0ff9a166/node_modules/vite-node/vite-node.mjs" "$@"
fi
