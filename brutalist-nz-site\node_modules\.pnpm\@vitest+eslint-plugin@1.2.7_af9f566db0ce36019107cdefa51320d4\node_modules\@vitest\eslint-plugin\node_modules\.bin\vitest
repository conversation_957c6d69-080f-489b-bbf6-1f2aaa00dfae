#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vitest@3.2.4_@types+node@22_0d864861c067eec09f63b55c7417dfd3/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vitest@3.2.4_@types+node@22_0d864861c067eec09f63b55c7417dfd3/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vitest@3.2.4_@types+node@22_0d864861c067eec09f63b55c7417dfd3/node_modules/vitest/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/vitest@3.2.4_@types+node@22_0d864861c067eec09f63b55c7417dfd3/node_modules:/mnt/c/Users/<USER>/projects/portcalm/brutalist-nz-site/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../../../vitest@3.2.4_@types+node@22_0d864861c067eec09f63b55c7417dfd3/node_modules/vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../../../../../../vitest@3.2.4_@types+node@22_0d864861c067eec09f63b55c7417dfd3/node_modules/vitest/vitest.mjs" "$@"
fi
